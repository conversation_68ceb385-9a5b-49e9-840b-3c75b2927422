/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-nhqmtxo7bh] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-nhqmtxo7bh] {
  color: #0077cc;
}

.btn-primary[b-nhqmtxo7bh] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-nhqmtxo7bh], .nav-pills .show > .nav-link[b-nhqmtxo7bh] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-nhqmtxo7bh] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-nhqmtxo7bh] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-nhqmtxo7bh] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-nhqmtxo7bh] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-nhqmtxo7bh] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
