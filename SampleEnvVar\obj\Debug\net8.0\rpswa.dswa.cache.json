{"GlobalPropertiesHash": "rEhz19XJgpLeFTnwHdOitnXeloRjW5tIsjxAN9YA4pc=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["yy/JzSYYQFIXHFX0XHTIyU6YWjm691PHz6rNNHBzevc=", "egdsuk53W25XTAYAqdYZn+NqNUB3EdIjX2BFmifwbG4=", "oBhiyjGE6SSbeyteypu2WiZaksbijwXhNjJ8uHQouk4=", "TIdHJf1gNKSD0AEz4hg44w6AS/N8qJ0c68nnonZwFm0=", "FNKHdd/uWcT8ZKU7e32SJyAZOTIViBiWafKfwzQZ07s=", "G1oLZ493g9RDzmqTfMzWMeGc3VI2KhPZQejUspCNDiI=", "+jLsjnmTEcItr+pq7O6rYL8MdDLLGkB7IHnv74xtu6U=", "Fjm4k/Gjg/mX+b6ASlrwFQ3Kx+KzdLqFknlnUJGbuNw=", "/wQZEybb8U6FLeU/73QuUNOQOL8vT8jWHgFvUvi/iBY=", "yGuuFL42+qE7jMBcX7R89iBZ+7UdGHPAX1okwVuymOg=", "ySn4Bn10Eo+YbjSNvos5RCkO/NVXj9C4Lbv11o4TP6Y=", "W7lO9qGiT3qbj4Hl3rkE+IhDtxMuNRr07hWjk4vNF1E=", "rvD5+Hwj5OqBsMWy+3uEXfCrQCyRcvca3UayDBVQxiU=", "i28wE13J7UbLcr5MQfAbhFhRCV88z4ytkZBokcWqiTs=", "ny28QF1iYGPSm16a/t4dxwSf2Wp5lKuNW1gOgDQXpAc=", "TQGxmoOAzDtYnDmwHVnTZpsoRVabN0BuefY0dyNU6f4=", "NMQ5boE780DOmJuSjDxD9rnZtJTmsn98IwRFk7Xh5bA=", "f9kuzDfxtgidPIqtNwziIUd+dHWEvPSM1zvTba7XzaI=", "CLGZ8Er3lIgsDNACV5yV6NY2yDuJMAxtti6QqBBqCCE=", "p17weEeq1d0nlDzNBrfl9l+CkYJmefYOaYbMdfxWE6U=", "Iy7qL3POwVTS2oL3ENRB2R1o8kg5eMNbaSURg36dFRI=", "yCNtrjWZImBPzoruPUo4jwBTlLb44CPnIAuSD44AHRg=", "MPwm/6cn0+RNx+Wa7g2IKsRs4pGjbzVMRxUCOlaE+ss=", "FP2DSvS3JWUe6pUtsQ2VacUjRq6hdUHKTzHMN3H+3v8=", "iUQuGK4/GnU/hU+MO7kvoYcbtv7IVl6K0IhulESaJQA=", "vRaswstqCOnyAeXK98pHFLqIPrKREdKzrIVFhj4535U=", "nY0+yp3Ey7XRM1q5CNBAYpw3UW6L5z/ICIqCoCSnfsI=", "xcNdNM35iagJBs/7VgXrIKVRFp5NHAjeX0arwxxlkmg=", "UcRq4wwUg255zjrqyacaCAuitZ/uw2QXiL06C1ASKrE=", "dpJSZ6QT/ZhhHWrzsVl2fuNiMKuX9vMP7NGiNjrjfds=", "6vnY1ggzt9NxdfK9tgMlhXNJFyaaSmXneCnBrvjZPiQ=", "86qI0d9mI6x9EEBH5BCrjUdozPSyThnLbvx+QmhEbqQ=", "TgJHrmy24X0Y0kJVR6VGI41mqopoSlaNoIIeLOSLfrk=", "bRSHNDY/1CmX7RgCuyjwg+r1Zy/tOgkmq5bB2DuC4wI=", "4ZTVb91a7wTKDcTcokp7r3gKPG139Y6qRiC8xSYeqHU=", "m4DvLwuJqWPy4SajfeyAnNsLsDoUBM1Mr4zQ7Hs/fFI=", "TIWK+XZNe+mYxm6IyAoLZyZMYZGYaU4e9FB2YVzpkAM=", "d8KpzMjZGPbFuaEr/fZikHrwYVRrCv3HGrkUcySgjHs=", "jrDLpzkQWIci9YEUufy9hB2Rm+Fc0MLDAkVxusVWWxs=", "PqqVkwD3OHXMhhNqvqBY4Spa/0N4BHGkWvOGbKu/mXI=", "AkeOlTrnzKLIcLmuGQcGHHUMysRpFvG/BIIFvaxnZok=", "R6yCqSPAaDtamnPWC4QqtlS6VkBJUhmKwr1wO3zwrBc=", "zobIjO0canmSRHnS4YgJKECgyIYNKd1yMVWivN6qj3s=", "WybIvCq5VNM/e0UUmj/3zinyqiL6RVJvbvLjJ/eDRK8=", "gveDYaSBTfrtvZoIwi9FpgrlesBNRSOBdEz/+lEla+c=", "d+Ny4WD5Zp2ao/Uv1NjPwyT5bTT7ACFdKAuI+cHtJbU=", "7H38jM/6xwnbYKfgg5apW8b+klru0Lh9FvM2lmxS1Vo=", "nSF/3nQwHUuk5Lcn6ZZrYBlVTrJiVPLQR//1oc2vvjk=", "ix/Ok9a7vhR5snulb9vkpMJWJE/N2Ql6Uqvvsh7CTJY=", "Cfq2QODZxykZc2xUpYNC8O+kcRfsmkleFG0ZK4hV7UY=", "bxkR4+vCJe+XKAhgJfBxU5dStIT2Ie+k+Sl23E/sPkU=", "gOA27zwThJkAG13foJn/dB4x1GeWb8pyu26p4ptaSe4=", "zb1CrjyZoTjAb/y2d7Ax08QWKkRRCuS3qjugMSTTMwQ=", "wB1F/kGwrclDNERZWy4lAxWXcusxI2ZctkXiCNSaouM=", "55i7J8zqVN7gnR3D5LpmnnZZnSItene85znKQqlc7Wg=", "KjVoEbUWQUvsiMZ+duecaKex8g6aO0lWgytat7GsHJE=", "fB6SvtvB6cUckoRaAECQDcyLKMdbP16gxlzphHdva0E=", "oaf98ibaRrfstqMv7BSbT6PUGySsW4tjPlDZK8YSMDY=", "Ii8KD0NHikidkw/ri3Su6C09FdtdJhDqE3HxxFfl0NY=", "IBxgFqpNn5+aJKQzKc6EzOrWy/mxZkrxweCM3s0+TAc=", "2t0wIKGYUkQ2CMlfqmEsyNVBIUIEtJPczV6HTRn513w=", "8X68YOGDHBnmxQIJYH6uVsAmVKi2Gm7thrWaHCl7tJw=", "W2LsshVeeYkFAKY0UcLJubJ1nrUUdZ1i0EVyKNOg+Zw=", "ScBknqQZSEp915nwLnZ+n3Op+31YW8ZZUCtViiVvClQ=", "vku35NzGk8yBPM3GGjHn/jN39nXikiuNJF1Q/2g34+Q=", "6r7gw3TUxmQSfLKHvHkE4CNqaJfYuti6N7+4WKL8ixY=", "aBQszkzKb3VC1qx73OInmefYsRyGcboN2Ay/9U+Glfw=", "QneDuLrCVQLTmGnp3YIoi19CHucoMXnEQwJ6s0YNVgw=", "gadq3FR/TGUh640DWWp4jE9YEqG8aDcQOoD+XFVhg4w=", "lnU3AfawG7vCkWeyLzOgVQ8lYIjdqKoKfpBtWEpUZIc="], "CachedAssets": {"yy/JzSYYQFIXHFX0XHTIyU6YWjm691PHz6rNNHBzevc=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\css\\site.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2tiyv64ts", "Integrity": "pAGv4ietcJNk/EwsQZ5BN9+K4MuNYS2a9wl4Jw+q9D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 362, "LastWriteTime": "2025-08-25T12:51:14.4697663+00:00"}, "egdsuk53W25XTAYAqdYZn+NqNUB3EdIjX2BFmifwbG4=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\favicon.ico", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-08-25T12:51:14.6040048+00:00"}, "oBhiyjGE6SSbeyteypu2WiZaksbijwXhNjJ8uHQouk4=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\js\\site.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-08-25T12:51:14.471169+00:00"}, "TIdHJf1gNKSD0AEz4hg44w6AS/N8qJ0c68nnonZwFm0=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-08-25T12:51:14.5457088+00:00"}, "FNKHdd/uWcT8ZKU7e32SJyAZOTIViBiWafKfwzQZ07s=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-08-25T12:51:14.5467193+00:00"}, "G1oLZ493g9RDzmqTfMzWMeGc3VI2KhPZQejUspCNDiI=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-08-25T12:51:14.5477148+00:00"}, "+jLsjnmTEcItr+pq7O6rYL8MdDLLGkB7IHnv74xtu6U=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-08-25T12:51:14.548705+00:00"}, "Fjm4k/Gjg/mX+b6ASlrwFQ3Kx+KzdLqFknlnUJGbuNw=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-08-25T12:51:14.548705+00:00"}, "/wQZEybb8U6FLeU/73QuUNOQOL8vT8jWHgFvUvi/iBY=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-08-25T12:51:14.5497042+00:00"}, "yGuuFL42+qE7jMBcX7R89iBZ+7UdGHPAX1okwVuymOg=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-08-25T12:51:14.5507059+00:00"}, "ySn4Bn10Eo+YbjSNvos5RCkO/NVXj9C4Lbv11o4TP6Y=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-08-25T12:51:14.5507059+00:00"}, "W7lO9qGiT3qbj4Hl3rkE+IhDtxMuNRr07hWjk4vNF1E=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-08-25T12:51:14.5517051+00:00"}, "rvD5+Hwj5OqBsMWy+3uEXfCrQCyRcvca3UayDBVQxiU=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-08-25T12:51:14.5537154+00:00"}, "i28wE13J7UbLcr5MQfAbhFhRCV88z4ytkZBokcWqiTs=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-08-25T12:51:14.5547061+00:00"}, "ny28QF1iYGPSm16a/t4dxwSf2Wp5lKuNW1gOgDQXpAc=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-08-25T12:51:14.5547061+00:00"}, "TQGxmoOAzDtYnDmwHVnTZpsoRVabN0BuefY0dyNU6f4=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-08-25T12:51:14.5557131+00:00"}, "NMQ5boE780DOmJuSjDxD9rnZtJTmsn98IwRFk7Xh5bA=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-08-25T12:51:14.5557131+00:00"}, "f9kuzDfxtgidPIqtNwziIUd+dHWEvPSM1zvTba7XzaI=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-08-25T12:51:14.5567135+00:00"}, "CLGZ8Er3lIgsDNACV5yV6NY2yDuJMAxtti6QqBBqCCE=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-08-25T12:51:14.5567135+00:00"}, "p17weEeq1d0nlDzNBrfl9l+CkYJmefYOaYbMdfxWE6U=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-08-25T12:51:14.5577153+00:00"}, "Iy7qL3POwVTS2oL3ENRB2R1o8kg5eMNbaSURg36dFRI=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-08-25T12:51:14.5587132+00:00"}, "yCNtrjWZImBPzoruPUo4jwBTlLb44CPnIAuSD44AHRg=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-08-25T12:51:14.5587132+00:00"}, "MPwm/6cn0+RNx+Wa7g2IKsRs4pGjbzVMRxUCOlaE+ss=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-08-25T12:51:14.5597121+00:00"}, "FP2DSvS3JWUe6pUtsQ2VacUjRq6hdUHKTzHMN3H+3v8=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-08-25T12:51:14.5597121+00:00"}, "iUQuGK4/GnU/hU+MO7kvoYcbtv7IVl6K0IhulESaJQA=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-08-25T12:51:14.5607122+00:00"}, "vRaswstqCOnyAeXK98pHFLqIPrKREdKzrIVFhj4535U=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-08-25T12:51:14.5617121+00:00"}, "nY0+yp3Ey7XRM1q5CNBAYpw3UW6L5z/ICIqCoCSnfsI=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-08-25T12:51:14.5617121+00:00"}, "xcNdNM35iagJBs/7VgXrIKVRFp5NHAjeX0arwxxlkmg=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-08-25T12:51:14.562712+00:00"}, "UcRq4wwUg255zjrqyacaCAuitZ/uw2QXiL06C1ASKrE=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-08-25T12:51:14.5657111+00:00"}, "dpJSZ6QT/ZhhHWrzsVl2fuNiMKuX9vMP7NGiNjrjfds=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-08-25T12:51:14.5657111+00:00"}, "6vnY1ggzt9NxdfK9tgMlhXNJFyaaSmXneCnBrvjZPiQ=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-08-25T12:51:14.567713+00:00"}, "86qI0d9mI6x9EEBH5BCrjUdozPSyThnLbvx+QmhEbqQ=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-08-25T12:51:14.5687119+00:00"}, "TgJHrmy24X0Y0kJVR6VGI41mqopoSlaNoIIeLOSLfrk=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-08-25T12:51:14.5711828+00:00"}, "bRSHNDY/1CmX7RgCuyjwg+r1Zy/tOgkmq5bB2DuC4wI=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-08-25T12:51:14.5721832+00:00"}, "4ZTVb91a7wTKDcTcokp7r3gKPG139Y6qRiC8xSYeqHU=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-08-25T12:51:14.5741836+00:00"}, "m4DvLwuJqWPy4SajfeyAnNsLsDoUBM1Mr4zQ7Hs/fFI=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-08-25T12:51:14.5751851+00:00"}, "TIWK+XZNe+mYxm6IyAoLZyZMYZGYaU4e9FB2YVzpkAM=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-08-25T12:51:14.5771858+00:00"}, "d8KpzMjZGPbFuaEr/fZikHrwYVRrCv3HGrkUcySgjHs=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-08-25T12:51:14.5781827+00:00"}, "jrDLpzkQWIci9YEUufy9hB2Rm+Fc0MLDAkVxusVWWxs=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-08-25T12:51:14.5801839+00:00"}, "PqqVkwD3OHXMhhNqvqBY4Spa/0N4BHGkWvOGbKu/mXI=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-08-25T12:51:14.581183+00:00"}, "AkeOlTrnzKLIcLmuGQcGHHUMysRpFvG/BIIFvaxnZok=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-08-25T12:51:14.5831827+00:00"}, "R6yCqSPAaDtamnPWC4QqtlS6VkBJUhmKwr1wO3zwrBc=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-08-25T12:51:14.5841856+00:00"}, "zobIjO0canmSRHnS4YgJKECgyIYNKd1yMVWivN6qj3s=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-08-25T12:51:14.5851853+00:00"}, "WybIvCq5VNM/e0UUmj/3zinyqiL6RVJvbvLjJ/eDRK8=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-08-25T12:51:14.5861859+00:00"}, "gveDYaSBTfrtvZoIwi9FpgrlesBNRSOBdEz/+lEla+c=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-08-25T12:51:14.5882666+00:00"}, "d+Ny4WD5Zp2ao/Uv1NjPwyT5bTT7ACFdKAuI+cHtJbU=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-08-25T12:51:14.5891851+00:00"}, "7H38jM/6xwnbYKfgg5apW8b+klru0Lh9FvM2lmxS1Vo=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-08-25T12:51:14.5911827+00:00"}, "nSF/3nQwHUuk5Lcn6ZZrYBlVTrJiVPLQR//1oc2vvjk=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-08-25T12:51:14.6001838+00:00"}, "ix/Ok9a7vhR5snulb9vkpMJWJE/N2Ql6Uqvvsh7CTJY=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-08-25T12:51:14.6080144+00:00"}, "Cfq2QODZxykZc2xUpYNC8O+kcRfsmkleFG0ZK4hV7UY=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-08-25T12:51:14.6100193+00:00"}, "bxkR4+vCJe+XKAhgJfBxU5dStIT2Ie+k+Sl23E/sPkU=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-08-25T12:51:14.6120168+00:00"}, "gOA27zwThJkAG13foJn/dB4x1GeWb8pyu26p4ptaSe4=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-08-25T12:51:14.4791697+00:00"}, "zb1CrjyZoTjAb/y2d7Ax08QWKkRRCuS3qjugMSTTMwQ=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-08-25T12:51:14.4791697+00:00"}, "wB1F/kGwrclDNERZWy4lAxWXcusxI2ZctkXiCNSaouM=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-08-25T12:51:14.4801684+00:00"}, "55i7J8zqVN7gnR3D5LpmnnZZnSItene85znKQqlc7Wg=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-08-25T12:51:14.4813905+00:00"}, "KjVoEbUWQUvsiMZ+duecaKex8g6aO0lWgytat7GsHJE=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-08-25T12:51:14.6050176+00:00"}, "fB6SvtvB6cUckoRaAECQDcyLKMdbP16gxlzphHdva0E=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-08-25T12:51:14.4741687+00:00"}, "oaf98ibaRrfstqMv7BSbT6PUGySsW4tjPlDZK8YSMDY=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-08-25T12:51:14.4751682+00:00"}, "Ii8KD0NHikidkw/ri3Su6C09FdtdJhDqE3HxxFfl0NY=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-08-25T12:51:14.4771708+00:00"}, "IBxgFqpNn5+aJKQzKc6EzOrWy/mxZkrxweCM3s0+TAc=": {"Identity": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "SampleEnvVar", "SourceType": "Discovered", "ContentRoot": "D:\\Project(s)\\Enterprise\\Explore\\SampleEnvVar\\SampleEnvVar\\wwwroot\\", "BasePath": "_content/SampleEnvVar", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-08-25T12:51:14.6022589+00:00"}}, "CachedCopyCandidates": {}}