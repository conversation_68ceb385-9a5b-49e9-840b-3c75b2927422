D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\bin\Debug\net8.0\appsettings.Development.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\bin\Debug\net8.0\appsettings.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\bin\Debug\net8.0\SampleEnvVar.staticwebassets.runtime.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\bin\Debug\net8.0\SampleEnvVar.staticwebassets.endpoints.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\bin\Debug\net8.0\SampleEnvVar.exe
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\bin\Debug\net8.0\SampleEnvVar.deps.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\bin\Debug\net8.0\SampleEnvVar.runtimeconfig.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\bin\Debug\net8.0\SampleEnvVar.dll
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\bin\Debug\net8.0\SampleEnvVar.pdb
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\rpswa.dswa.cache.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.GeneratedMSBuildEditorConfig.editorconfig
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.AssemblyInfoInputs.cache
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.AssemblyInfo.cs
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.csproj.CoreCompileInputs.cache
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.MvcApplicationPartsAssemblyInfo.cache
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.RazorAssemblyInfo.cache
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.RazorAssemblyInfo.cs
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\rjimswa.dswa.cache.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\scopedcss\Views\Shared\_Layout.cshtml.rz.scp.css
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\scopedcss\bundle\SampleEnvVar.styles.css
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\scopedcss\projectbundle\SampleEnvVar.bundle.scp.css
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\staticwebassets.build.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\staticwebassets.build.json.cache
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\staticwebassets.development.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\staticwebassets.build.endpoints.json
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.dll
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\refint\SampleEnvVar.dll
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.pdb
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\SampleEnvVar.genruntimeconfig.cache
D:\Project(s)\Enterprise\Explore\SampleEnvVar\SampleEnvVar\obj\Debug\net8.0\ref\SampleEnvVar.dll
